import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:provider/provider.dart';

class ExtractDetailsMiddleStatic extends StatelessWidget {
  const ExtractDetailsMiddleStatic({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<WebHomeProviderStatic>(
      builder: (context, provider, child) {
        return Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.white,
          child: Column(
            children: [
              // Header with toggle
              _buildHeader(context, provider),

              // Content area
              Expanded(
                child: _buildContent(context, provider),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, WebHomeProviderStatic provider) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: [
          // Objects label
          Text(
            'Objects',
            style: TextStyle(
              fontFamily: FontManager.fontFamilyInter,
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),

          const SizedBox(width: 16),

          // Toggle switch
          _buildToggleSwitch(context, provider),

          const SizedBox(width: 16),

          // Mode labels
          Row(
            children: [
              Text(
                'AI Mode',
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontSize: ResponsiveFontSizes.titleMedium(context),
                  fontWeight: FontWeight.w500,
                  color: provider.isAIMode ? Colors.black : Colors.grey,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '/',
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontSize: ResponsiveFontSizes.titleMedium(context),
                  color: Colors.grey,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Manual Mode',
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontSize: ResponsiveFontSizes.titleMedium(context),
                  fontWeight: FontWeight.w500,
                  color: !provider.isAIMode ? Colors.black : Colors.grey,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildToggleSwitch(
      BuildContext context, WebHomeProviderStatic provider) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          provider.toggleAIMode();
        },
        child: Container(
          width: 44,
          height: 24,
          decoration: BoxDecoration(
            color:
                provider.isAIMode ? const Color(0xFF0058FF) : Colors.grey[300],
            borderRadius: BorderRadius.circular(12),
          ),
          child: AnimatedAlign(
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeInOut,
            alignment: provider.isAIMode
                ? Alignment.centerRight
                : Alignment.centerLeft,
            child: Container(
              width: 20,
              height: 20,
              margin: const EdgeInsets.all(2),
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, WebHomeProviderStatic provider) {
    if (provider.isAIMode) {
      return _buildDiscoveryTab(context);
    } else {
      return _buildDevelopmentTab(context);
    }
  }

  Widget _buildDiscoveryTab(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Tab header
          Text(
            'Discovery',
            style: TextStyle(
              fontFamily: FontManager.fontFamilyInter,
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),

          const SizedBox(height: 24),

          // Content items
          _buildContentItem(
            context,
            'Policy has Policy ID - Policy Number, Product',
            'Policy has Policy ID - Policy Number, Product',
          ),

          const SizedBox(height: 16),

          _buildContentItem(
            context,
            'Customer has Customer ID - Name, email',
            'Customer has Customer ID - Name, email',
          ),

          const SizedBox(height: 16),

          _buildContentItem(
            context,
            'Product has product ID - Product Number',
            'Product has product ID - Product Number',
          ),
        ],
      ),
    );
  }

  Widget _buildDevelopmentTab(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Tab header
          Text(
            'Development',
            style: TextStyle(
              fontFamily: FontManager.fontFamilyInter,
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),

          const SizedBox(height: 24),

          // Development content
          Text(
            'Manual development mode content goes here.',
            style: TextStyle(
              fontFamily: FontManager.fontFamilyInter,
              fontSize: ResponsiveFontSizes.titleMedium(context),
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentItem(
      BuildContext context, String title, String subtitle) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          // Icon
          Container(
            width: 8,
            height: 8,
            decoration: const BoxDecoration(
              color: Color(0xFF0058FF),
              shape: BoxShape.circle,
            ),
          ),

          const SizedBox(width: 12),

          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontSize: ResponsiveFontSizes.titleMedium(context),
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
                if (subtitle != title) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyInter,
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
